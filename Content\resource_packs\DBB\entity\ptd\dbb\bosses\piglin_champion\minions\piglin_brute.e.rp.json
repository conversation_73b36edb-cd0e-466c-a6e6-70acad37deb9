{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:piglin_brute", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ptd/dbb/entity/bosses/piglin_champion/minions/piglin_brute"}, "geometry": {"default": "geometry.ptd_dbb_piglin_brute"}, "animations": {"look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_dbb_piglin_brute.spawn", "idle": "animation.ptd_dbb_piglin_brute.idle", "idle_2": "animation.better_piglin_brute.idle_2", "idle_3": "animation.better_piglin_brute.idle_3", "walk": "animation.ptd_dbb_piglin_brute.walk", "run": "animation.ptd_dbb_piglin_brute.run", "vertical_attack": "animation.ptd_dbb_piglin_brute.vertical_attack", "horizontal_attack": "animation.better_piglin_brute.horizontal_attack", "damaged": "animation.ptd_dbb_piglin_brute.damaged", "death": "animation.ptd_dbb_piglin_brute.death", "dead": "animation.ptd_dbb_piglin_brute.dead", "general": "controller.animation.ptd_dbb_piglin_brute.general"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", {"look_at_target": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}]}, "render_controllers": ["controller.render.ptd_dbb_default"], "spawn_egg": {"base_color": "#a57892", "overlay_color": "#979b27"}}}}