import { Entity, system } from "@minecraft/server";

/**
 * Configuration for event-based death mechanics
 */
export interface EventBasedDeathMechanicsConfig {
  /** Duration of death animation in ticks */
  duration: number;
  /** XP orb configuration */
  xpOrbs?: {
    count: number;
    duration: number;
    heightOffset?: number;
  };
  /** Items to drop */
  drops?: Array<{
    item: string;
    count: number;
    heightOffset?: number;
  }>;
  /** Sound to play when death starts */
  deathSound?: string;
  /** Custom events to trigger at specific ticks */
  customEvents?: Array<{
    tick: number;
    callback: (entity: Entity) => void;
  }>;
  /** Function to stop entity sounds */
  stopSoundsFn?: (entity: Entity, excludedSound?: string) => void;
}

/**
 * Handles event-based death mechanics for bosses
 * This replaces the interval-based death system with a runTimeout-based approach
 * @param entity The entity that died
 * @param config Death mechanics configuration
 */
export function handleEventBasedDeathMechanics(entity: Entity, config: EventBasedDeathMechanicsConfig): void {
  try {
    const isDead = entity.getProperty("ptd_dbb:dead") as boolean;
    
    if (!isDead) {
      return;
    }

    // Stop all sounds except death sound
    if (config.stopSoundsFn) {
      config.stopSoundsFn(entity, config.deathSound);
    }

    // Play death sound
    if (config.deathSound) {
      entity.dimension.playSound(config.deathSound, entity.location);
    }

    // Save death location
    const deathLocation = entity.location;
    entity.setDynamicProperty("ptd_dbb:death_location", JSON.stringify(deathLocation));

    // Schedule XP orb spawning
    if (config.xpOrbs) {
      const xpTimeout = system.runTimeout(() => {
        try {
          const isDead = entity.getProperty("ptd_dbb:dead") as boolean;
          if (!isDead) {
            system.clearRun(xpTimeout);
            return;
          }

          // Spawn XP orbs
          for (let i = 0; i < config.xpOrbs!.count; i++) {
            const xpSpawnTimeout = system.runTimeout(() => {
              try {
                const savedLocation = entity.getDynamicProperty("ptd_dbb:death_location") as string;
                const location = savedLocation ? JSON.parse(savedLocation) : entity.location;
                
                const xpLocation = {
                  x: location.x + (Math.random() - 0.5) * 2,
                  y: location.y + (config.xpOrbs?.heightOffset || 1),
                  z: location.z + (Math.random() - 0.5) * 2
                };

                entity.dimension.spawnEntity("minecraft:xp_orb", xpLocation);
                system.clearRun(xpSpawnTimeout);
              } catch (error) {
                system.clearRun(xpSpawnTimeout);
              }
            }, i * 2); // Spawn every 2 ticks
          }
          
          system.clearRun(xpTimeout);
        } catch (error) {
          system.clearRun(xpTimeout);
        }
      }, config.xpOrbs.duration);
    }

    // Schedule item drops
    if (config.drops && config.drops.length > 0) {
      config.drops.forEach((drop, index) => {
        const dropTimeout = system.runTimeout(() => {
          try {
            const isDead = entity.getProperty("ptd_dbb:dead") as boolean;
            if (!isDead) {
              system.clearRun(dropTimeout);
              return;
            }

            const savedLocation = entity.getDynamicProperty("ptd_dbb:death_location") as string;
            const location = savedLocation ? JSON.parse(savedLocation) : entity.location;
            
            const dropLocation = {
              x: location.x,
              y: location.y + (drop.heightOffset || 1),
              z: location.z
            };

            for (let i = 0; i < drop.count; i++) {
              entity.dimension.spawnItem(drop.item, dropLocation);
            }
            
            system.clearRun(dropTimeout);
          } catch (error) {
            system.clearRun(dropTimeout);
          }
        }, 10 + index * 5); // Stagger drops
      });
    }

    // Schedule custom events
    if (config.customEvents) {
      config.customEvents.forEach((customEvent) => {
        const eventTimeout = system.runTimeout(() => {
          try {
            const isDead = entity.getProperty("ptd_dbb:dead") as boolean;
            if (!isDead) {
              system.clearRun(eventTimeout);
              return;
            }

            customEvent.callback(entity);
            system.clearRun(eventTimeout);
          } catch (error) {
            system.clearRun(eventTimeout);
          }
        }, customEvent.tick);
      });
    }

    // Schedule entity removal at the end
    const removalTimeout = system.runTimeout(() => {
      try {
        entity.remove();
        system.clearRun(removalTimeout);
      } catch (error) {
        // Entity might already be removed
        system.clearRun(removalTimeout);
      }
    }, config.duration);

  } catch (error) {
    // Entity might have been removed
  }
}
