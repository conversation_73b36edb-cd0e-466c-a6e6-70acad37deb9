import { Entity, EntityComponentTypes, system } from "@minecraft/server";
import { shockwave } from "../general_attacks/shockwave";
import { handleEventBasedDeathMechanics } from "../general_mechanics/eventBasedDeathMechanics";
import { selectAttack } from "./controller";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";
import { spawnItemFountain } from "../general_mechanics/itemFountain";
import { cameraShake } from "../general_effects/camerashake";

/**
 * Handles the piglin champion spawning mechanics using event-based system
 * @param piglinChampion The piglin champion entity
 */
export function handlePiglinChampionSpawning(piglinChampion: Entity): void {
  // Apply shockwave effect on tick 6
  const shockwaveTimeout = system.runTimeout(() => {
    try {
      const isSpawning = piglinChampion.getProperty("ptd_dbb:spawning") as boolean;
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;

      if (!isSpawning || isDead) {
        system.clearRun(shockwaveTimeout);
        return;
      }

      // Apply shockwave with radius 8, power 1.5, damage 8, excluding piglin_champion family
      shockwave(piglinChampion, 5, 1.5, 8, ["piglin_champion"]);

      // Apply camera shake effect
      cameraShake(piglinChampion, 32, 0.02, 0.5, 0.5);

      // Play a particle effect at the piglin's location
      piglinChampion.dimension.spawnParticle("minecraft:large_explosion", piglinChampion.location);

      // Play a sound effect
      piglinChampion.dimension.playSound("random.explode", piglinChampion.location);

      system.clearRun(shockwaveTimeout);
    } catch (error) {
      system.clearRun(shockwaveTimeout);
    }
  }, 6); // Tick 6

  // Spawn final particle effect near the end of the animation
  const particleTimeout = system.runTimeout(() => {
    try {
      const isSpawning = piglinChampion.getProperty("ptd_dbb:spawning") as boolean;
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;

      if (!isSpawning || isDead) {
        system.clearRun(particleTimeout);
        return;
      }

      // Play the final particle effect at the piglin's location
      piglinChampion.dimension.spawnParticle("ptd_dbb:pg_spawn3_01", piglinChampion.location);

      system.clearRun(particleTimeout);
    } catch (error) {
      system.clearRun(particleTimeout);
    }
  }, 48); // Tick 48
}

/**
 * Handles the piglin champion attack selection using event-based system
 * @param piglinChampion The piglin champion entity
 */
export function handlePiglinChampionAttackSelection(piglinChampion: Entity): void {
  try {
    const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
    const isSpawning = piglinChampion.getProperty("ptd_dbb:spawning") as boolean;
    const attack = piglinChampion.getProperty("ptd_dbb:attack") as string;
    const coolingDown = piglinChampion.getProperty("ptd_dbb:cooling_down") as boolean;

    // Don't select attacks if dead, spawning, already attacking, or cooling down
    if (isDead || isSpawning || attack !== "none" || coolingDown) {
      return;
    }

    // Get target for attack selection
    const target = piglinChampion.getTarget();
    if (!target) {
      return;
    }

    // Check health for healing ability and stun mechanics
    const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
    const health = healthComponent?.currentValue || 0;
    const maxHealth = healthComponent?.defaultValue || 0;
    const lastHealThreshold = piglinChampion.getProperty("ptd_dbb:last_heal_threshold") as number;
    const stunStandingTriggered = piglinChampion.getProperty("ptd_dbb:stun_standing_triggered") as boolean;
    const stunSittingTriggered = piglinChampion.getProperty("ptd_dbb:stun_sitting_triggered") as boolean;

    // Calculate current health percentage threshold (0-3 for 75%, 50%, 25%, 0%)
    const currentThreshold = Math.floor((health / maxHealth) * 4);

    // Calculate health percentage for stun mechanics
    const healthPercentage = (health / maxHealth) * 100;

    // Check for stun standing at 65% health
    if (
      healthPercentage <= 65 &&
      !stunStandingTriggered &&
      attack !== "stunned_standing" &&
      attack !== "stunned_sitting" &&
      !isDead
    ) {
      // Stop all other sound effects except for stunned standing sound
      const stunSound = ATTACK_SOUND_MAP["stunned_standing"];
      stopPiglinChampionSounds(piglinChampion, stunSound);

      // Trigger stunned standing
      piglinChampion.triggerEvent("ptd_dbb:stunned_standing");

      // Apply slowness effect for the duration of the stun
      piglinChampion.addEffect("minecraft:slowness", 240, { amplifier: 250, showParticles: false });

      return; // Exit early to prevent other actions this tick
    }

    // Check for stun sitting at 35% health
    if (healthPercentage <= 35 && !stunSittingTriggered && attack !== "stunned_sitting" && !isDead) {
      // Stop all other sound effects except for stunned sitting sound
      const stunSound = ATTACK_SOUND_MAP["stunned_sitting"];
      stopPiglinChampionSounds(piglinChampion, stunSound);

      // Trigger stunned sitting
      piglinChampion.triggerEvent("ptd_dbb:stunned_sitting");

      // Apply slowness effect for the duration of the stun
      piglinChampion.addEffect("minecraft:slowness", 333, { amplifier: 250, showParticles: false });

      return; // Exit early to prevent other actions this tick
    }

    // Check if we need to heal first
    if (currentThreshold < lastHealThreshold) {
      // Update last heal threshold
      piglinChampion.setProperty("ptd_dbb:last_heal_threshold", currentThreshold);

      // Stop all other sound effects except for healing sound
      const healingSound = ATTACK_SOUND_MAP["healing"];
      stopPiglinChampionSounds(piglinChampion, healingSound);

      // Trigger healing ability
      piglinChampion.triggerEvent("ptd_dbb:healing_ability");

      // Apply slowness with amplifier 250 for the duration of the healing animation
      piglinChampion.addEffect("minecraft:slowness", 158, { amplifier: 250, showParticles: false });
    } else {
      // Otherwise select a normal attack
      selectAttack(piglinChampion, target);
    }
  } catch (error) {
    // Entity might have been removed
  }
}

/**
 * Handles the piglin champion death mechanics using event-based system
 * @param piglinChampion The piglin champion entity
 */
export function handlePiglinChampionDeath(piglinChampion: Entity): void {
  handleEventBasedDeathMechanics(piglinChampion, {
    // Configure death mechanics specific to the Piglin Champion
    duration: 100,
    xpOrbs: {
      count: 8,
      duration: 30,
      heightOffset: 2.25,
    },
    // No drops here as we'll use a custom event to spawn the essence fountain
    drops: [],
    deathSound: "mob.ptd_dbb_piglin_champion.death",
    // Add custom event to spawn essence fountain at the beginning of death sequence
    customEvents: [
      {
        tick: 1,
        callback: (entity: Entity) => {
          piglinChampion.dimension.spawnParticle("ptd_dbb:pg_die1_01", entity.location);
          // Spawn 32 essence items in a fountain-like effect
          spawnItemFountain(entity, "ptd_dbb:piglin_champion_essence", 32, {
            heightOffset: 2.25,
            particleEffect: "minecraft:large_explosion",
            soundEffect: "random.pop",
            minVerticalStrength: 0.1,
            maxVerticalStrength: 0.3,
            minHorizontalStrength: 0.05,
            maxHorizontalStrength: 0.2,
          });
        },
      },
    ],
    // Provide the sound stopping function
    stopSoundsFn: stopPiglinChampionSounds,
  });
}
