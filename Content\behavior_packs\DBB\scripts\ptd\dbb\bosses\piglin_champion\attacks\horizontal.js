import { EntityDamageCause, GameMode, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { stopPiglinChampionSounds } from "../soundManager";
/**
 * Executes the horizontal attack for the Piglin Champion
 * Applies damage and knockback to nearby entities based on the piglin's direction
 *
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (not used for attack positioning)
 */
export function executeHorizontalAttack(piglinChampion) {
    // Apply damage to nearby entities
    const damageRadius = 4;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.horizontal.damage;
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 7 blocks in front of the piglin
    const originPos = {
        x: piglinChampion.location.x + dirX * 7,
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + dirZ * 7
    };
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
        .forEach((entity) => {
        // Apply damage
        entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
        // Use piglin's direction for knockback
        // Create 2D points (same y-coordinate) to calculate horizontal distance
        const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
        const point2 = { x: originPos.x, y: 0, z: originPos.z };
        const distance = getDistance(point1, point2);
        if (distance > 0) {
            // Use the piglin's direction for knockback
            const nx = dirX;
            const nz = dirZ;
            // Horizontal attack parameters
            const horizontalStrength = 7.0; // Increased to knock players back 8 blocks
            const verticalStrength = 0.8;
            try {
                // Try to apply knockback first
                if (entity instanceof Player) {
                    const gameMode = entity.getGameMode();
                    if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                else {
                    entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
            }
            catch (e) {
                // Fallback to applyImpulse if applyKnockback fails
                const impulse = {
                    x: nx * horizontalStrength,
                    y: verticalStrength,
                    z: nz * horizontalStrength
                };
                entity.applyImpulse(impulse);
            }
        }
    });
}
/**
 * Event-based horizontal attack for the Piglin Champion
 * Uses runTimeout pattern: Attack → Reset → Cooldown
 * @param piglinChampion The piglin champion entity
 */
export function executeEventBasedHorizontalAttack(piglinChampion) {
    const ANIMATION_TIME = 104; // 5.2 seconds at 20 ticks per second
    const DAMAGE_TIMING = 38; // When to apply damage
    const COOLDOWN_TIME = 40; // Cooldown after attack
    // Apply slowness effect for the duration of the attack
    piglinChampion.addEffect("minecraft:slowness", ANIMATION_TIME, { amplifier: 250, showParticles: false });
    // Schedule damage application at the right timing
    const damageTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (isDead || currentAttack !== "horizontal") {
                system.clearRun(damageTimeout);
                return;
            }
            // Execute the actual attack
            executeHorizontalAttack(piglinChampion);
            system.clearRun(damageTimeout);
        }
        catch (error) {
            system.clearRun(damageTimeout);
        }
    }, DAMAGE_TIMING);
    // Schedule attack reset
    const resetTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTimeout);
                return;
            }
            // Stop all sounds when resetting attack
            stopPiglinChampionSounds(piglinChampion);
            // Reset attack
            piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            system.clearRun(resetTimeout);
        }
        catch (error) {
            system.clearRun(resetTimeout);
        }
    }, ANIMATION_TIME);
    // Schedule cooldown end
    const cooldownTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTimeout);
                return;
            }
            // End cooldown
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldownTimeout);
        }
        catch (error) {
            system.clearRun(cooldownTimeout);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
