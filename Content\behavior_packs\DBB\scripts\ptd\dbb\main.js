import { Block, Entity, system, world } from "@minecraft/server";
import { handleCameraShakeScriptEvent } from "./bosses/general_effects/camerashake";
import { initializeItemCustomComponents } from "./items/index";
import { checkBossSummoningRequirements } from "./bosses/general_mechanics/bossSummoner";
import { grimhowlMechanics } from "./bosses/grimhowl/index";
import "./entities/index"; // Import entities module for initialization
import "./player/index"; // Import player module for initialization
// Creates a Set of dimension references for quick lookup.
export const dimensions = new Set(["overworld", "nether", "the_end"].map((dimension) => world.getDimension(dimension)));
// Initialize custom components
world.beforeEvents.worldInitialize.subscribe((data) => {
    const itemComponentRegistry = data.itemComponentRegistry;
    initializeItemCustomComponents(itemComponentRegistry);
});
/**
 * Main game loop initialization
 * Sets up event listeners and periodic updates for boss mechanics
 */
system.runTimeout(() => {
    // Schedule a timeout to start the interval
    // MAIN TICK
    system.runInterval(() => {
        dimensions.forEach((dimension) => {
            // Iterate over each dimension
            // Check for boss summoning requirements
            checkBossSummoningRequirements(dimension);
        });
    });
    // Listen for script events
    system.afterEvents.scriptEventReceive.subscribe((data) => {
        const entity = data.sourceEntity;
        const block = data.sourceBlock;
        const eventId = data.id;
        if (entity)
            scriptEventDispatcher(entity, eventId);
        if (block)
            scriptEventDispatcher(block, eventId);
        return;
    });
    function scriptEventDispatcher(source, eventId) {
        if (source instanceof Entity) {
            const entityTypeId = source.typeId;
            switch (eventId) {
                // Handle camera shake events
                case "ptd_dbb:camerashake":
                    handleCameraShakeScriptEvent(source);
                    break;
                // Handle horizontal attack events
                case "ptd_dbb:horizontal_attack_end":
                    if (source.typeId === "ptd_dbb:piglin_champion") {
                        source.triggerEvent("ptd_dbb:reset_attack");
                    }
                    break;
                // Handle vertical attack events
                case "ptd_dbb:vertical_attack_end":
                    if (source.typeId === "ptd_dbb:piglin_champion") {
                        source.triggerEvent("ptd_dbb:reset_attack");
                    }
                    break;
                default:
                    break;
            }
            if (entityTypeId === "ptd_dbb:grimhowl") {
                grimhowlMechanics({ id: eventId, sourceEntity: source }, "script");
            }
        }
        else if (source instanceof Block) {
            // Handle block-specific script events
            return; // Just return for now since we don't have any custom blocks that fire scriptevents
        }
        return;
    }
}, 60); // Initial delay of 60 ticks before starting the interval
