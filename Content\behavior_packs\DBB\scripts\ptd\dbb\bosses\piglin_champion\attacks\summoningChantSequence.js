import { system } from "@minecraft/server";
import { executeSummoningChantAttack } from "./summoning_chant";
import { stopPiglinChampionSounds } from "../soundManager";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Attack timing and duration constants for summoning chant attack
 */
const DAMAGE_TIMING = 40; // When summoning occurs (ticks)
const ANIMATION_TIME = 125; // Total animation duration (ticks)
const COOLDOWN_TIME = 100; // Cooldown after attack (ticks)
/**
 * Execute summoning chant attack with runTimeout-based timing
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional, will be found if not provided)
 */
export function executeSummoningChantAttackSequence(piglinChampion, target) {
    // Schedule summoning at the correct timing
    const summonTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(summonTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "summoning_chant") {
                // Get fresh target if needed
                const currentTarget = target || getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
                // Execute summoning chant with target information
                executeSummoningChantAttack(piglinChampion, currentTarget);
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(summonTimeout);
        }
    }, DAMAGE_TIMING);
    // Schedule attack reset when animation completes
    const resetTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTimeout);
                return;
            }
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (currentAttack === "summoning_chant") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(resetTimeout);
        }
    }, ANIMATION_TIME);
    // Schedule cooldown completion
    const cooldownTimeout = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTimeout);
                return;
            }
            // Set cooling_down to false to allow next attack selection
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            // Entity might have been removed
            system.clearRun(cooldownTimeout);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
