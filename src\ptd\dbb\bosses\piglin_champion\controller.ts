import { Entity } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";
import { executeEventBasedHorizontalAttack } from "./attacks/horizontal";
import { getAvailableAttacks, updateAttackHistory, displayAttackHistory, SHORT_RANGE_ATTACKS, MEDIUM_RANGE_ATTACKS, LONG_RANGE_ATTACKS } from "./attackTracker";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";

/**
 * Counts the number of minions belonging to a specific piglin champion within a given radius
 * @param piglinChampion The piglin champion entity
 * @param radius The search radius in blocks (default: 64)
 * @returns The number of minions belonging to this piglin champion
 */
export function countPiglinChampionMinions(piglinChampion: Entity, radius: number = 64): number {
    // Get the piglin champion's unique ID
    const championId = piglinChampion.id;

    // Search for piglin minions in the area
    const piglin_brutes = piglinChampion.dimension.getEntities({
        location: piglinChampion.location,
        maxDistance: radius,
        type: "ptd_dbb:piglin_brute"
    });

    const piglin_marauders = piglinChampion.dimension.getEntities({
        location: piglinChampion.location,
        maxDistance: radius,
        type: "ptd_dbb:piglin_marauder"
    });

    // Count minions that belong to this specific piglin champion
    let count = 0;
    for (const minion of piglin_brutes) {
        const minionChampionId = minion.getDynamicProperty("ptd_dbb:champion_id") as string;
        if (minionChampionId === championId) {
            count++;
        }
    }

    for (const minion of piglin_marauders) {
        const minionChampionId = minion.getDynamicProperty("ptd_dbb:champion_id") as string;
        if (minionChampionId === championId) {
            count++;
        }
    }

    return count;
}

/**
 * Attack durations in ticks (20 ticks per second)
 * The total duration of the animation
 */
export const ATTACK_DURATIONS: Record<string, number> = {
    horizontal: 104, // 5.2 seconds at 20 ticks per second
    vertical: 106,     // 5.3 seconds at 20 ticks per second
    foot_stomp: 80,     // 4.0 seconds at 20 ticks per second
    spin_slam: 178,     // 8.9 seconds at 20 ticks per second
    body_slam: 186,     // 9.3 seconds at 20 ticks per second
    upchuck: 170,       // 8.5 seconds at 20 ticks per second
    charging: 315,      // 15.75 seconds at 20 ticks per second
    healing: 158,       // 7.9 seconds at 20 ticks per second
    summoning_chant: 125, // 6.25 seconds at 20 ticks per second
    stunned_standing: 240, // 12 seconds at 20 ticks per second
    stunned_sitting: 333  // 16.65 seconds at 20 ticks per second
};

/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS: Record<string, number> = {
    horizontal: 38,
    vertical: 44,
    foot_stomp: 24,
    spin_slam_phase1: 83,
    spin_slam_phase2: 124,
    body_slam: 63,
    upchuck: 55,
    charging_continuous: 46,  // Start continuous damage during charge movement
    charging: 96,             // Final impact damage
    healing_phase1: 43,
    healing_phase2: 130,
    summoning_chant: 40,
    stunned_standing_phase1: 30,  // End of damage_to_stunned
    stunned_standing_phase2: 150, // End of stunned_standing (30 + 120)
    stunned_sitting_phase1: 30,   // End of damage_to_stunned
    stunned_sitting_phase2: 150   // End of stunned_sitting (30 + 120)
};

/**
 * Attack range boundaries in blocks
 * Defines strict minimum and maximum distances for each attack range category
 */
export const ATTACK_RANGES = {
    close: { min: 0, max: 5 },    // 0-5 blocks: all attack types available
    medium: { min: 5, max: 7 },   // 5-7 blocks: all attack types with different probabilities
    long: { min: 7, max: 8 },    // 7-8 blocks: vertical attack, upchuck, and charging
    unreachable: { min: 12, max: 32 }  // 12-32 blocks: charging attack and summoning chant
} as const;

/**
 * Executes an event-based attack based on attack type
 * @param piglinChampion The piglin champion entity
 * @param attack The attack type to execute
 * @param target The target entity (optional for some attacks)
 */
export function executeEventBasedAttack(piglinChampion: Entity, attack: string, target?: Entity): void {
    switch (attack) {
        case "horizontal":
            executeEventBasedHorizontalAttack(piglinChampion);
            break;
        // TODO: Add other event-based attack functions as they are created
        default:
            // Fallback to triggering the event for attacks not yet converted
            piglinChampion.triggerEvent(`ptd_dbb:${attack}_attack`);
            break;
    }
}

/**
 * Selects an attack based on target distance using event-based system
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 */
export function selectAttack(piglinChampion: Entity, target: Entity): void {
    const distance = getDistance(piglinChampion.location, target.location);
    let selectedAttack: string | null = null;

    // Close range (0-5 blocks): select from available attacks based on usage history
    if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(piglinChampion, SHORT_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack: string = availableAttacks[randomIndex]!;
            selectedAttack = attack;

            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopPiglinChampionSounds(piglinChampion, attackSound);

            // Set attack property and execute event-based attack
            piglinChampion.setProperty("ptd_dbb:attack", attack);
            executeEventBasedAttack(piglinChampion, attack, target);

            // Update attack history
            updateAttackHistory(piglinChampion, attack);
            // Display attack history on the actionbar
            displayAttackHistory(piglinChampion);
        }
    }
    // Medium range (4-8 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
        // Get available attacks based on usage history
        let availableAttacks = getAvailableAttacks(piglinChampion, MEDIUM_RANGE_ATTACKS);

        // Check minion count for summoning_chant attack
        const minionCount = countPiglinChampionMinions(piglinChampion, 64);
        if (minionCount > 1) {
            // Remove summoning_chant from available attacks if there are more than 1 minion
            availableAttacks = availableAttacks.filter(attack => attack !== "summoning_chant");
        }

        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack: string = availableAttacks[randomIndex]!;
            selectedAttack = attack;

            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopPiglinChampionSounds(piglinChampion, attackSound);

            // Set attack property and execute event-based attack
            piglinChampion.setProperty("ptd_dbb:attack", attack);
            executeEventBasedAttack(piglinChampion, attack, target);

            // Update attack history
            updateAttackHistory(piglinChampion, attack);
            // Display attack history on the actionbar
            displayAttackHistory(piglinChampion);
        }
    }
    // Long range (8-12 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
        // Get available attacks based on usage history
        let availableAttacks = getAvailableAttacks(piglinChampion, LONG_RANGE_ATTACKS);

        // Check minion count for summoning_chant attack
        const minionCount = countPiglinChampionMinions(piglinChampion, 64);
        if (minionCount > 1) {
            // Remove summoning_chant from available attacks if there are more than 1 minion
            availableAttacks = availableAttacks.filter(attack => attack !== "summoning_chant");
        }

        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack: string = availableAttacks[randomIndex]!;
            selectedAttack = attack;

            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopPiglinChampionSounds(piglinChampion, attackSound);

            // Set attack property and execute event-based attack
            piglinChampion.setProperty("ptd_dbb:attack", attack);
            executeEventBasedAttack(piglinChampion, attack, target);

            // Update attack history
            updateAttackHistory(piglinChampion, attack);
            // Display attack history on the actionbar
            displayAttackHistory(piglinChampion);
        }
    }
    // Unreachable range (12+ blocks): charging attack or summoning_chant
    else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
        // 20% chance for an attack, 80% chance to not attack
        // this is evaluated per tick
        if (Math.random() < 0.2) {
            // Check minion count for summoning_chant attack
            const minionCount = countPiglinChampionMinions(piglinChampion, 64);

            // If there are 0-1 minions, allow summoning_chant (20% chance), otherwise only charging
            if (minionCount <= 1 && Math.random() < 0.2) {
                selectedAttack = "summoning_chant";

                // Stop all other sound effects except for this attack's sound
                const attackSound = ATTACK_SOUND_MAP["summoning_chant"];
                stopPiglinChampionSounds(piglinChampion, attackSound);

                // Set attack property and execute event-based attack
                piglinChampion.setProperty("ptd_dbb:attack", "summoning_chant");
                executeEventBasedAttack(piglinChampion, "summoning_chant", target);
            } else {
                selectedAttack = "charging";

                // Stop all other sound effects except for this attack's sound
                const attackSound = ATTACK_SOUND_MAP["charging"];
                stopPiglinChampionSounds(piglinChampion, attackSound);

                // Set attack property and execute event-based attack
                piglinChampion.setProperty("ptd_dbb:attack", "charging");
                executeEventBasedAttack(piglinChampion, "charging", target);
            }
        }
    }
}

